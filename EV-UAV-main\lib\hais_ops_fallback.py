import torch
import numpy as np

class HAIS_OP_Fallback:
    @staticmethod
    def voxelize_idx(coords, output_coords, input_map, output_map, batchsize, mode):
        """
        Fallback implementation for voxelize_idx
        """
        # Simple voxelization without CUDA operations
        device = coords.device
        N = coords.size(0)
        
        # Create a simple mapping
        unique_coords, inverse_indices = torch.unique(coords, dim=0, return_inverse=True)
        M = unique_coords.size(0)
        
        # Set output_coords
        output_coords.resize_(M, coords.size(1))
        output_coords.copy_(unique_coords)
        
        # Set input_map (mapping from input to output)
        input_map.resize_(N)
        input_map.copy_(inverse_indices.int())
        
        # Create output_map (for each output voxel, which input points belong to it)
        max_points_per_voxel = 100  # arbitrary limit
        output_map.resize_(M, max_points_per_voxel + 1)
        output_map.fill_(-1)
        
        # Count points per voxel
        for i in range(M):
            mask = (inverse_indices == i)
            indices = torch.where(mask)[0]
            count = min(len(indices), max_points_per_voxel)
            output_map[i, 0] = count
            if count > 0:
                output_map[i, 1:count+1] = indices[:count].int()
    
    @staticmethod
    def voxelize_fp(feats, output_feats, map_rule, mode, M, maxActive, C):
        """
        Fallback implementation for voxelize_fp
        """
        # Simple feature aggregation
        for i in range(M):
            count = map_rule[i, 0].item()
            if count > 0:
                indices = map_rule[i, 1:count+1]
                valid_indices = indices[indices >= 0]
                if len(valid_indices) > 0:
                    if mode == 4:  # mean
                        output_feats[i] = feats[valid_indices].mean(dim=0)
                    else:  # sum
                        output_feats[i] = feats[valid_indices].sum(dim=0)
    
    @staticmethod
    def voxelize_bp(d_output_feats, d_feats, map_rule, mode, M, maxActive, C):
        """
        Fallback implementation for voxelize_bp
        """
        # Simple backward pass
        for i in range(M):
            count = map_rule[i, 0].item()
            if count > 0:
                indices = map_rule[i, 1:count+1]
                valid_indices = indices[indices >= 0]
                if len(valid_indices) > 0:
                    if mode == 4:  # mean
                        d_feats[valid_indices] += d_output_feats[i] / len(valid_indices)
                    else:  # sum
                        d_feats[valid_indices] += d_output_feats[i]

# Create a fallback HAIS_OP object
HAIS_OP = HAIS_OP_Fallback()
